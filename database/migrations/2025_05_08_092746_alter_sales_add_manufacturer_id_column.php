<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->foreignId('manufacturer_id')->nullable()->after('dealership_id')->constrained()->nullOnDelete();
        });

        \App\Models\Sale::query()->where('vehicle_make', 'Mercedes-Benz')->update(['vehicle_make' => 'Mercedes']);

        \App\Models\Sale::query()->update([
            'manufacturer_id' => DB::raw('(SELECT id FROM manufacturers WHERE name = vehicle_make)'),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropConstrainedForeignId('manufacturer_id');
        });
    }
};
