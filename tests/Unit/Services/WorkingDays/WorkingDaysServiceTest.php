<?php

use App\Services\WorkingDays\UkBankHolidayService;
use App\Services\WorkingDays\WorkingDaysService;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $this->bankHolidayService = Mockery::mock(UkBankHolidayService::class);
    $this->workingDaysService = new WorkingDaysService($this->bankHolidayService);
});

describe('addWorkingDays', function () {
    it('adds working days correctly when no weekends or holidays are involved', function () {
        // Monday 2024-01-01 + 3 working days = Thursday 2024-01-04
        $startDate = Carbon::parse('2024-01-01'); // Monday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturn(false);

        $result = $this->workingDaysService->addWorkingDays($startDate, 3);

        expect($result->toDateString())->toBe('2024-01-04'); // Thursday
    });

    it('skips weekends when adding working days', function () {
        // Friday 2024-01-05 + 3 working days = Wednesday 2024-01-10 (skipping weekend)
        $startDate = Carbon::parse('2024-01-05'); // Friday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturn(false);

        $result = $this->workingDaysService->addWorkingDays($startDate, 3);

        expect($result->toDateString())->toBe('2024-01-10'); // Wednesday
    });

    it('skips bank holidays when adding working days', function () {
        // Monday 2024-01-01 + 2 working days, but Tuesday is a bank holiday
        $startDate = Carbon::parse('2024-01-01'); // Monday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturnUsing(function (Carbon $date) {
                return $date->toDateString() === '2024-01-02'; // Tuesday is a bank holiday
            });

        $result = $this->workingDaysService->addWorkingDays($startDate, 2);

        expect($result->toDateString())->toBe('2024-01-04'); // Thursday (skipping bank holiday Tuesday)
    });

    it('uses current date when start date is null', function () {
        Carbon::setTestNow('2024-01-01 10:00:00'); // Monday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturn(false);

        $result = $this->workingDaysService->addWorkingDays(null, 1);

        expect($result->toDateString())->toBe('2024-01-02'); // Tuesday
    });

    it('returns same date when adding zero working days', function () {
        $startDate = Carbon::parse('2024-01-01');

        $result = $this->workingDaysService->addWorkingDays($startDate, 0);

        expect($result->toDateString())->toBe('2024-01-01');
    });

    it('throws exception for negative working days', function () {
        $startDate = Carbon::parse('2024-01-01');

        expect(fn () => $this->workingDaysService->addWorkingDays($startDate, -1))
            ->toThrow(InvalidArgumentException::class, 'Working days must be a non-negative integer');
    });
});

describe('subtractWorkingDays', function () {
    it('subtracts working days correctly', function () {
        // Friday 2024-01-05 - 3 working days = Tuesday 2024-01-02
        $startDate = Carbon::parse('2024-01-05'); // Friday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturn(false);

        $result = $this->workingDaysService->subtractWorkingDays($startDate, 3);

        expect($result->toDateString())->toBe('2024-01-02'); // Tuesday
    });

    it('skips weekends when subtracting working days', function () {
        // Monday 2024-01-08 - 3 working days = Wednesday 2024-01-03 (skipping weekend)
        $startDate = Carbon::parse('2024-01-08'); // Monday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturn(false);

        $result = $this->workingDaysService->subtractWorkingDays($startDate, 3);

        expect($result->toDateString())->toBe('2024-01-03'); // Wednesday
    });
});

describe('isWorkingDay', function () {
    it('returns true for weekdays that are not bank holidays', function () {
        $monday = Carbon::parse('2024-01-01'); // Monday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->with($monday)
            ->andReturn(false);

        expect($this->workingDaysService->isWorkingDay($monday))->toBeTrue();
    });

    it('returns false for weekends', function () {
        $saturday = Carbon::parse('2024-01-06'); // Saturday
        $sunday = Carbon::parse('2024-01-07'); // Sunday

        expect($this->workingDaysService->isWorkingDay($saturday))->toBeFalse();
        expect($this->workingDaysService->isWorkingDay($sunday))->toBeFalse();
    });

    it('returns false for bank holidays', function () {
        $monday = Carbon::parse('2024-01-01'); // Monday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->with($monday)
            ->andReturn(true);

        expect($this->workingDaysService->isWorkingDay($monday))->toBeFalse();
    });
});

describe('countWorkingDaysBetween', function () {
    it('counts working days between two dates correctly', function () {
        // From Monday 2024-01-01 to Friday 2024-01-05 (exclusive) = 3 working days
        $startDate = Carbon::parse('2024-01-01'); // Monday
        $endDate = Carbon::parse('2024-01-05'); // Friday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturn(false);

        $result = $this->workingDaysService->countWorkingDaysBetween($startDate, $endDate);

        expect($result)->toBe(3); // Tuesday, Wednesday, Thursday
    });

    it('returns zero when start date is after or equal to end date', function () {
        $date1 = Carbon::parse('2024-01-05');
        $date2 = Carbon::parse('2024-01-01');

        expect($this->workingDaysService->countWorkingDaysBetween($date1, $date2))->toBe(0);
        expect($this->workingDaysService->countWorkingDaysBetween($date1, $date1))->toBe(0);
    });
});

describe('getNextWorkingDay', function () {
    it('returns next working day', function () {
        $friday = Carbon::parse('2024-01-05'); // Friday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturn(false);

        $result = $this->workingDaysService->getNextWorkingDay($friday);

        expect($result->toDateString())->toBe('2024-01-08'); // Monday (skipping weekend)
    });
});

describe('getPreviousWorkingDay', function () {
    it('returns previous working day', function () {
        $monday = Carbon::parse('2024-01-08'); // Monday

        $this->bankHolidayService
            ->shouldReceive('isBankHoliday')
            ->andReturn(false);

        $result = $this->workingDaysService->getPreviousWorkingDay($monday);

        expect($result->toDateString())->toBe('2024-01-05'); // Friday (skipping weekend)
    });
});
