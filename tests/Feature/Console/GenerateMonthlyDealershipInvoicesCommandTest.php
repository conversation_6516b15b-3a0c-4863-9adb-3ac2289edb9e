<?php

use App\Console\Commands\GenerateMonthlyDealershipInvoicesCommand;
use App\Models\Account;
use App\Models\Dealership;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Sale;
use App\Services\Accounting\InvoiceData;
use App\Services\Accounting\XeroAccountingService;
use App\Services\Payments\DirectDebit\DirectDebitPaymentProcessor;
use App\Services\Payments\DirectDebit\PaymentData;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Notification;
use Mockery\MockInterface;

describe('subscription monthly payments', function () {

    test('monthly dealership invoices include sales from the correct period', function ($date, $runsCount) {
        $currentDate = now();

        Carbon::setTestNow($date);

        $this->mock(XeroAccountingService::class, function (MockInterface $mock) {
            $mock->shouldReceive('createInvoice')->once()
                ->withArgs(function (InvoiceData $invoiceData) {
                    expect($invoiceData)
                        ->status->toBe('AUTHORISED')
                        ->date->toBe(now()->startOfMonth()->subDay()->toDateString())
                        ->dueDate->toBe(now()->startOfMonth()->toDateString())
                        ->lineItems->count()->toBe(6)
                        ->lineItems->get(0)->description->toContain('Warranty')
                        ->lineItems->get(0)->quantity->toEqual(1)
                        ->lineItems->get(0)->accountCode->toBe(config('accounting.nominal_codes.warranty.managed_fund_sales.code'))
                        ->lineItems->get(0)->unitAmount->toEqual(20)
                        ->lineItems->get(0)->taxAmount->toEqual(4)
                        ->lineItems->get(1)->description->toContain('Breakdown Plan')
                        ->lineItems->get(1)->quantity->toEqual(1)
                        ->lineItems->get(1)->accountCode->toBe(config('accounting.nominal_codes.breakdown.managed_fund_revenue.code'))
                        ->lineItems->get(1)->unitAmount->toEqual(99)
                        ->lineItems->get(1)->taxAmount->toEqual(19.80)
                        ->lineItems->get(2)->description->toContain('Service Plan')
                        ->lineItems->get(2)->quantity->toEqual(1)
                        ->lineItems->get(2)->accountCode->toBe(config('accounting.nominal_codes.service_plan.admin_fees.code'))
                        ->lineItems->get(2)->unitAmount->toEqual(399)
                        ->lineItems->get(2)->taxAmount->toEqual(79.80);

                    return true;
                })
                ->andReturnUsing(function (InvoiceData $invoiceData) {
                    $invoiceData->id = 'SOME-XERO-INVOICE-ID';
                    $invoiceData->invoiceNumber = 'INV-1234';

                    return $invoiceData;
                });
            $mock->shouldReceive('emailInvoice')->once()->withArgs(['SOME-XERO-INVOICE-ID'])->andReturn(true);
        });

        $this->mock(DirectDebitPaymentProcessor::class, function (MockInterface $mock) {
            $mock->shouldReceive('createPayment')
                ->once()
                ->withArgs(function (Payment $payment) {
                    expect($payment)
                        ->provider->toBe(\App\Enums\PaymentProvider::ACCESS_PAYSUITE)
                        ->period_start->toBeNull()
                        ->amount->toEqual(1243.20)
                        ->charge_date->toBeNull()
                        ->payable->accounting_software_id->toBe('SOME-XERO-INVOICE-ID');

                    return true;
                })
                ->andReturnUsing(function (Payment $payment) {
                    return new PaymentData(
                        provider: \App\Enums\PaymentProvider::ACCESS_PAYSUITE,
                        providerPaymentId: 'SOME-PAYMENT-PROCESSOR-ID',
                        chargeDate: today()->addWeeks(2),
                        status: Payment::STATUS_PENDING_SUBMISSION,
                        amount: 0,
                    );
                });
        });

        $account = Account::factory()->create();

        $sales = Sale::factory()
            ->times(2)
            ->confirmed($account)
            ->for(Dealership::withoutEvents(fn () => Dealership::factory()
                ->for(\App\Models\BillingRequest::factory())
                ->recycle($account)
                ->create())
            )
            ->hasWarranty(function ($attributes, $sale) {
                return [
                    'provision' => 100,
                    'admin_fee' => 20,
                    'vat' => 4,
                    'selling_price' => 200,
                    'is_self_funded' => false,
                ];
            })
            ->hasBreakdownPlan(function ($attributes, $sale) {
                return [
                    'provision' => 119,
                    'admin_fee' => 99,
                    'vat' => 19.80,
                    'selling_price' => 299,
                    'is_self_funded' => false,
                ];
            })
            ->hasServicePlan(function ($attributes, $sale) {
                return [
                    'admin_fee' => 399,
                    'vat' => 79.80,
                    'selling_price' => 599,
                ];
            })
            ->create();

        Carbon::setTestNow($currentDate);

        for ($i = 0; $i < $runsCount; $i++) {
            Artisan::call(GenerateMonthlyDealershipInvoicesCommand::class);
        }

        expect(Invoice::count())->toBe(1)
            ->and(Invoice::latest()->first())
            ->account_id->toBe($sales->first()->account_id)
            ->invoiceable_type->toBe('dealership')
            ->invoice_number->toBe('INV-1234')
            ->accounting_software_id->toBe('SOME-XERO-INVOICE-ID')
            ->date->toEqual(now()->startOfMonth()->subDay())
            ->due_date->toEqual(now()->startOfMonth())
            ->emailed_at->toEqual(now()->toDateTimeString())
            ->getTotal()->toEqual(1243.20)
            ->lineItems->count()->toBe(6)
            ->lineItems->get(0)->type->toBe('warranty')
            ->lineItems->get(0)->quantity->toBe(1)
            ->lineItems->get(0)->account_code->toBe(config('accounting.nominal_codes.warranty.managed_fund_sales.code'))
            ->lineItems->get(0)->unit_amount->toEqual(20)
            ->lineItems->get(0)->tax->toEqual(4)
            ->lineItems->get(1)->type->toBe('breakdown')
            ->lineItems->get(1)->quantity->toBe(1)
            ->lineItems->get(1)->account_code->toBe(config('accounting.nominal_codes.breakdown.managed_fund_revenue.code'))
            ->lineItems->get(1)->unit_amount->toEqual(99)
            ->lineItems->get(1)->tax->toEqual(19.80)
            ->lineItems->get(2)->type->toBe('service_plan')
            ->lineItems->get(2)->quantity->toBe(1)
            ->lineItems->get(2)->account_code->toBe(config('accounting.nominal_codes.service_plan.admin_fees.code'))
            ->lineItems->get(2)->unit_amount->toEqual(399)
            ->lineItems->get(2)->tax->toEqual(79.80);

        expect(Payment::count())->toBe(1)
            ->and(Payment::latest()->first())
            ->provider->toBe(\App\Enums\PaymentProvider::ACCESS_PAYSUITE)
            ->amount->toEqual(1243.20)
            ->payable_id->toBe(Invoice::latest()->first()->id)
            ->payable_type->toBe(\Illuminate\Database\Eloquent\Relations\Relation::getMorphAlias(Invoice::class))
            ->processor_payment_id->toBe('SOME-PAYMENT-PROCESSOR-ID')
            ->charge_date->eq(today()->addWeeks(2))->toBeTrue()
            ->status->toBe(Payment::STATUS_PENDING_SUBMISSION);

    })
        ->with([
            'sales at start of valid period' => [now()->startOfMonth()->subMonth()],
            'sales at end of valid period' => [now()->startOfMonth()->subMonth()->endOfMonth()],
        ])
        ->with([
            'call command once' => [1],
            'call command twice' => [2],
        ]);

    test('monthly dealership invoice do not include sales from other periods', function ($date) {
        Notification::fake();

        $mock = $this->mock(XeroAccountingService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('createInvoice');
            $mock->shouldNotReceive('emailInvoice');
        });

        $this->mock(DirectDebitPaymentProcessor::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('createPayment');
        });

        $account = Account::factory()->create();

        $sales = Sale::factory()
            ->times(2)
            ->confirmed()
            ->recycle($account)
            ->for(Dealership::factory()->create())
            ->hasWarranty(function ($attributes, $sale) {
                return [
                    'provision' => 100,
                    'admin_fee' => 20,
                    'vat' => 4,
                    'selling_price' => 200,
                    'is_self_funded' => false,
                ];
            })
            ->hasBreakdownPlan(function ($attributes, $sale) {
                return [
                    'provision' => 119,
                    'admin_fee' => 99,
                    'vat' => 19.80,
                    'selling_price' => 299,
                    'is_self_funded' => false,
                ];
            })
            ->hasServicePlan(function ($attributes, $sale) {
                return [
                    'admin_fee' => 399,
                    'vat' => 79.80,
                    'selling_price' => 599,
                ];
            })
            ->create();

        Carbon::setTestNow();

        Artisan::call(GenerateMonthlyDealershipInvoicesCommand::class);

        expect(Invoice::count())->toBe(0);

    })->with([
        'sales in earlier period' => [now()->endOfMonth()->subMonths(2)],
        'sales in later period' => [now()->startOfMonth()],
    ]);

});
