<?php

namespace Tests;

use App\Services\Payments\DirectDebit\GoCardlessDirectDebitPaymentProcessor;
use Database\Seeders\RoleAndPermissionSeeder;
use Illuminate\Database\Events\DatabaseRefreshed;
use Illuminate\Foundation\Testing\LazilyRefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Event;
use Spatie\Permission\PermissionRegistrar;

abstract class TestCase extends BaseTestCase
{
    use LazilyRefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Event::listen(DatabaseRefreshed::class, function () {
            $this->artisan('db:seed', ['--class' => RoleAndPermissionSeeder::class]);
            $this->app->make(PermissionRegistrar::class)->forgetCachedPermissions();
        });

        $this->mock(GoCardlessDirectDebitPaymentProcessor::class);
    }
}
