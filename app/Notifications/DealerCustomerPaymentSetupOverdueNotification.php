<?php

namespace App\Notifications;

use App\Enums\PaymentMethod;
use App\Filament\Pages\OperationsDashboard;
use App\Models\Sale;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class DealerCustomerPaymentSetupOverdueNotification extends Notification //implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public Collection $sales) {}

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->subject("{$this->sales->count()} {$this->plural($this->sales->count())} overdue setting up their payment method.");

        $grouped = $this->sales->groupBy(fn(Sale $sale) => $sale->getPaymentMethod()->name);

        if ($count = $grouped->get(PaymentMethod::PAY_LATER->name)?->count()) {
            $message->line("{$count} {$this->plural($count)} overdue setting up their Pay Later agreement.");
        }
        if ($count = $grouped->get(PaymentMethod::DIRECT_DEBIT->name)?->count()) {
            $message->line("{$count} {$this->plural($count)} overdue setting up their Direct Debit.");
        }

        $message->action('View affected sales', OperationsDashboard::getUrl());

        $message->line('Please note that there may be more customers than mentioned in this email; this email contains only deals entered by you.');

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }

    private function plural(int $count):string
    {
        return $count === 1 ? 'customer is' : 'customers are';
    }
}
