<?php

namespace App\Policies;

use App\Models\AuthorisedService;
use App\Models\User;

class AuthorisedServicePolicy
{
    public function before(User $user): ?bool
    {
        if (! $user->isViewingAllRecords() &&
            AuthorisedService::whereHas('servicePlan', fn ($query) => $query->where('account_id', $user->account_id))->doesntExist()) {

            return false;
        }

        return null;
    }

    public function viewAny(User $user): bool
    {
        return $user->can('service-plan-redemptions.view');
    }

    public function view(User $user, AuthorisedService $authorisedService): bool
    {
        return $user->can('service-plan-redemptions.view');
    }

    public function create(User $user): bool
    {
        return $user->can('service-plan-redemptions.create');
    }

    public function update(User $user, AuthorisedService $authorisedService): bool
    {
        return $user->can('service-plan-redemptions.update');
    }

    public function delete(User $user, AuthorisedService $authorisedService): bool
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return $user->can('service-plan-redemptions.delete');
    }
}
