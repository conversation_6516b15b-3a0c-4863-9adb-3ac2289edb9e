<?php

namespace App\Policies;

use App\Models\PayLaterService;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;

class PayLaterServicePolicy
{
    use HandlesAuthorization;

    public function before()
    {
//        return false;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('pay-later-services.view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PayLaterService $payLaterService): bool
    {
        return $user->can('pay-later-services.view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('pay-later-services.create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, PayLaterService $payLaterService): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, PayLaterService $payLaterService): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, PayLaterService $payLaterService): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, PayLaterService $payLaterService): bool
    {
        return false;
    }
}
