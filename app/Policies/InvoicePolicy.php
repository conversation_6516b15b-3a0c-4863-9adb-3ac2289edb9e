<?php

namespace App\Policies;

use App\Models\Invoice;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class InvoicePolicy
{
    use HandlesAuthorization;

    public function before()
    {
        return false;
    }

    /**
     * Determine whether the user can view any model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user): bool
    {
        return $user->can('invoices.view');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Invoice $invoice): bool
    {
        return $user->can('invoices.view');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user): bool
    {
        return $user->can('invoices.create');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Invoice $invoice): bool
    {
        return $user->can('invoices.delete');
    }

    /**
     * Determine whether the user can view the model in the accounting software.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewInAccountingSoftware(User $user, Invoice $invoice)
    {
        return $user->isAdmin();
    }
}
