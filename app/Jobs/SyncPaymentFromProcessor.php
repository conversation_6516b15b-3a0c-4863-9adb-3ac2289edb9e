<?php

namespace App\Jobs;

use App\Actions\CreateXeroBankTransactionForPayment;
use App\Actions\CreateXeroInvoicePayment;
use App\Actions\DeleteXeroBankTransactionForPayment;
use App\Actions\DeleteXeroInvoicePayment;
use App\Models\Invoice;
use App\Models\Payment;
use App\Services\Payments\DirectDebit\DirectDebitPaymentProcessor;
use App\Models\Sale;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SyncPaymentFromProcessor implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public Payment $payment) {}

    /**
     * Execute the job.
     */
    public function handle(
        DirectDebitPaymentProcessor $paymentProcessor,
        CreateXeroBankTransactionForPayment $createXeroBankTransactionForPayment,
        DeleteXeroBankTransactionForPayment $deleteXeroBankTransactionForPayment,
        CreateXeroInvoicePayment $createXeroInvoicePayment,
        DeleteXeroInvoicePayment $deleteXeroInvoicePayment,
    ): void {
        if (! $this->payment->processor_payment_id) {
            throw new \Exception("Payment {$this->payment->id} has not been sent to processor");
        }

        $processorPayment = $paymentProcessor->getPayment($this->payment);

        $this->payment->update([
            'status' => match ($processorPayment->status) {
                'unpaid' => Payment::STATUS_FAILED,
                'paid' => Payment::STATUS_PAID,
                default => $processorPayment->status,
            },
            'provider' => $processorPayment->provider,
            'processor_payment_id' => $processorPayment->providerPaymentId,
            'charge_date' => $processorPayment->chargeDate,
        ]);

        if ($this->payment->payable instanceof Sale) {
            // This is a subscription payment
            if ($this->payment->isPaid() && ! $this->payment->account_software_bank_transfer_id) {
                // paid in processor but not in Xero
                $createXeroBankTransactionForPayment->onQueue()->execute($this->payment);
            } elseif ($this->payment->isUnpaid() && $this->payment->account_software_bank_transfer_id) {
                // not paid in processor but is in Xero
                $deleteXeroBankTransactionForPayment->onQueue()->execute($this->payment);
            }
        }

        if ($this->payment->payable instanceof Invoice && $this->payment->payable?->accounting_software_id) {
            if ($this->payment->isPaid() && ! $this->payment->payable->accounting_software_payment_id) {
                // This is an invoice in Xero that has been paid and not yet been marked as paid in Xero.
                $createXeroInvoicePayment->onQueue()->execute($this->payment->payable);
            } elseif ($this->payment->isUnpaid() && $this->payment->payable->accounting_software_payment_id) {
                // This is an invoice that has been marked as paid in Xero, but not in processor.
                $deleteXeroInvoicePayment->onQueue()->execute($this->payment->payable);
            }
        }
    }
}
