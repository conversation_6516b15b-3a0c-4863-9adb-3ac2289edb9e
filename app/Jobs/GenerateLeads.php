<?php

namespace App\Jobs;

use App\Models\Sale;
use App\Models\SalesLead;
use App\Services\LeadGenerator\LeadGeneratorContract;
use App\Services\LeadGenerator\WarrantyRenewalLeadGenerator;
use App\Services\LeadGenerator\WarrantyUpsellLeadGenerator;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class GenerateLeads implements ShouldQueue
{
    use Queueable;

    protected $generators = [
        WarrantyRenewalLeadGenerator::class,
        WarrantyUpsellLeadGenerator::class,
    ];

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        foreach($this->generators as $generatorClass) {
            $generator = $this->factory($generatorClass);

            $sales = $generator
                ->query(Sale::query()->whereDoesntHave('salesLead'))
                ->get()
                ->map(fn (Sale $sale) => [
                    'upselling_sale_id' => $sale->id,
                    'created_by_user_id' => auth()->id(),
                    'assigned_to_user_id' => null,
                ]);

            dd($sales->toArray());

            SalesLead::query()->upsert($sales, ['upselling_sale_id']);
        }
    }


    private function factory(mixed $generatorClass):LeadGeneratorContract
    {
        return app($generatorClass);
    }
}
