<?php

namespace App\Livewire;

use App\Models\Customer;
use App\Models\PhoneCall;
use App\Services\Voip\Voip3cxProvider;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Livewire\Component;
use Filament\Tables;

class ListPhoneCalls extends Component implements HasForms, HasTable
{
    use InteractsWithTable;
    use InteractsWithForms;

    public Customer $customer;

    public function table(Table $table): Table
    {
        return $table
            ->heading('Phone Calls')
            ->relationship(fn (): HasMany => $this->customer->phoneCalls()->with('transcript'))
            ->inverseRelationship('customer')
            ->defaultSort('started_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('started_at')
                    ->sortable()
                    ->dateTime(),
                Tables\Columns\TextColumn::make('type')
                    ->getStateUsing(fn (PhoneCall $record) => $record->source_type === 0 ? 'Out' : 'In'),
                Tables\Columns\TextColumn::make('user.name'),
                Tables\Columns\TextColumn::make('ringing_duration')
                    ->sortable()
                    ->formatStateUsing(fn ($state) => gmdate('H:i:s', (int) round($state))),
                Tables\Columns\TextColumn::make('talking_duration')
                    ->sortable()
                    ->formatStateUsing(fn ($state) => gmdate('H:i:s', (int) round($state))),
                Tables\Columns\IconColumn::make('answered')
                    ->alignCenter()
                    ->boolean(),
                Tables\Columns\TextColumn::make('reason')->wrap(),
            ])
            ->filters([
                // ...
            ])
            ->actions([
                Tables\Actions\Action::make('show_transcript')
                    ->hiddenLabel()
                    ->icon('heroicon-o-document')
                    ->visible(fn (PhoneCall $call) => $call->transcript)
                    ->infolist([
                        TextEntry::make('transcript.text')->columnSpanFull(),
                    ])
                    ->modalFooterActions(fn() =>[]),
                Tables\Actions\Action::make('download_recording')
                    ->hiddenLabel()
                    ->icon('heroicon-o-arrow-down-tray')
                    ->visible(fn (PhoneCall $call) => $call->recording_id)
                    ->action(fn(Tables\Actions\Action $action, PhoneCall $call, Voip3cxProvider $voip) => $action
                        ->redirect($voip->recordingUrl($call->recording_id))
                    ),
            ]);
    }

    public function render(): View
    {
        return view('livewire.list-phone-calls');
    }
}
