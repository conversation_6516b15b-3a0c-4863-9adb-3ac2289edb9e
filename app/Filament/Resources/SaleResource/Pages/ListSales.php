<?php

namespace App\Filament\Resources\SaleResource\Pages;

use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\SaleResource;
use App\Filament\Traits\HasParentResource;
use App\Filament\Widgets\SalesOverview;
use App\Models\BreakdownPlan;
use App\Models\Warranty;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Gate;

class ListSales extends ListRecords
{
    use HasParentResource;

    protected static string $resource = SaleResource::class;

    protected ?string $maxContentWidth = 'full';

    protected function getHeaderActions(): array
    {
        return [
            ActionGroup::make([
                Action::make('Warranties Export')
                    ->visible(Gate::allows('exportAll', Warranty::class))
                    ->url(fn () => route('reports.warranties', [
                        'start' => $this->tableFilters['confirmed_at']['date_from'] ?? null,
                        'end' => $this->tableFilters['confirmed_at']['date_to'] ?? null,
                    ]))
                    ->openUrlInNewTab(),
                Action::make('Breakdown Plans Export')
                    ->visible(Gate::allows('exportAll', BreakdownPlan::class))
                    ->url(fn () => route('reports.breakdown-plans', [
                        'start' => $this->tableFilters['confirmed_at']['date_from'] ?? null,
                        'end' => $this->tableFilters['confirmed_at']['date_to'] ?? null,
                    ]))
                    ->openUrlInNewTab(),
            ])->label('Exports')->button(),
            CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            SalesOverview::class,
        ];
    }
}
