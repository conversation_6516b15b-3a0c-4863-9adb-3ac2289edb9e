<?php

namespace App\Filament\Resources\PayLaterServiceResource\Pages;

use App\Actions\PayLater\BeginPayLaterApplication;
use App\Actions\PayLater\PreApproveSaleForPayLater;
use App\Filament\Fields\CustomerLookup;
use App\Filament\Fields\VRMInput;
use App\Filament\Resources\PayLaterServiceResource;
use App\Filament\Resources\SaleResource\Pages\CreateSale;
use App\Models\PayLaterPlan;
use App\Models\PayLaterService;
use App\Services\Payments\PayLater\PayLaterPaymentProcessor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Pages\Concerns\HasWizard;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\MaxWidth;
use Livewire\Attributes\Url;

class CreatePayLaterService extends CreateRecord
{
    use HasWizard;

    protected static string $resource = PayLaterServiceResource::class;

    protected static bool $canCreateAnother = false;

    #[Url(as: 'customer', except: '')]
    public $customerId = null;

    public function form(Form $form): Form
    {
        return $form
            ->columns(null)
            ->schema([
                Section::make('Customer')
                    ->columns(2)
                    ->schema([
                        CustomerLookup::make('customer_id')
                            ->default($this->customerId)
                            ->afterStateUpdated(fn ($state) => $this->customerId = $state ?: ''),
                    ]),
                Wizard::make($this->getSteps())
                    ->cancelAction($this->getCancelFormAction())
                    ->submitAction($this->getSubmitFormAction())
                    ->skippable($this->hasSkippableSteps()),
                //                    ]),
            ]);
    }

    protected function getSteps(): array
    {
        return [
            ...CreateSale::lookupVehicleSteps(),
            Step::make('Service Information')
                ->columns(null)
                ->lazy()
                ->afterStateUpdated(function (Get $get, Set $set, PayLaterPaymentProcessor $payLaterPaymentProcessor) {
                    $payLaterPlanId = $get('pay_later_plan_id');
                    $total = $get('invoice_amount');
                    $deposit = $get('deposit_amount');

                    if (! $payLaterPlanId || ! $total || ! $deposit) {
                        return;
                    }

                    $payLaterPlan = PayLaterPlan::find($payLaterPlanId);

                    $instalments = $payLaterPlan->instalments;

                    $instalmentAmount = (($total - $deposit) / ($instalments - 1) * 100) / 100;

                    $depositToDealer = $deposit - $instalmentAmount;

                    $loanAmount = $total - $depositToDealer;

                    $set('invoice_amount', number_format($total, 2, thousands_separator: ''));
                    $set('deposit_amount', number_format($deposit, 2, thousands_separator: ''));
                    $set('first_payment', number_format($instalmentAmount, 2, thousands_separator: ''));
                    $set('deposit_to_dealer', number_format($depositToDealer, 2, thousands_separator: ''));

                    $set('plan_breakdown', $payLaterPaymentProcessor
                        ->forAccount(auth()->user()->account)
                        ->getPlanBreakdown(PayLaterPlan::find($payLaterPlanId), $loanAmount)->forHumans());
                })
                ->schema([
                    Textarea::make('description')
                        ->label('Description of product, service or work to be carried out')
                        ->columnSpanFull()
                        ->required(),
                    Select::make('pay_later_plan_id')
                        ->label('Pay Later Plan')
                        ->options(
                            PayLaterPlan::where('deposit', true)
                                ->active()
                                ->where('account_id', auth()->user()->account_id)
                                ->pluck('name', 'id')
                        )
                        ->maxWidth(MaxWidth::ExtraSmall)
                        ->native(false)
                        ->preload()
                        ->required(),
                    TextInput::make('invoice_amount')
                        ->maxWidth(MaxWidth::ExtraSmall)
                        ->required()
                        ->label('The total amount of the service invoice')
                        ->prefix('£')
                        ->numeric(),
                    TextInput::make('deposit_amount')
                        ->maxWidth(MaxWidth::ExtraSmall)
                        ->required()
                        ->label('The available deposit for the customer')
                        ->prefix('£')
                        ->numeric(),
                    TextInput::make('deposit_to_dealer')
                        ->maxWidth(MaxWidth::ExtraSmall)
                        ->disabled()
                        ->label('Payable immediately to the dealer')
                        ->prefix('£')
                        ->numeric(),
                    TextInput::make('first_payment')
                        ->maxWidth(MaxWidth::ExtraSmall)
                        ->disabled()
                        ->label('Payable immediately to the pay later service')
                        ->prefix('£')
                        ->numeric(),
                    Textarea::make('plan_breakdown')
                        ->columnSpanFull()
                        ->disabled(),
                ]),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        unset($data['pay_later_plan_id']);
        unset($data['description']);

        // Set account_id from the authenticated user
        $data['account_id'] = auth()->user()->account_id;

        return $data;
    }

    protected function afterCreate()
    {
        /** @var PayLaterService $payLaterService */
        $payLaterService = $this->getRecord();

        $payLaterAgreement = app(PreApproveSaleForPayLater::class)->execute($payLaterService);

        $payLaterPlan = $payLaterService->account->payLaterPlans()->findOrFail($this->data['pay_later_plan_id']);

        $payLaterAgreement->update([
            ...$payLaterPlan->only(['commission_rate', 'commission_rate_margin']),
            'pay_later_plan_id' => $payLaterPlan->getKey(),
            'deposit_amount' => $payLaterService->deposit_amount,
            'loan_amount' => $payLaterService->invoice_amount - $payLaterService->deposit_amount,
            'description' => $this->data['description'],
        ]);

        app(BeginPayLaterApplication::class)->execute($payLaterAgreement, sendNotification: true);
    }
}
