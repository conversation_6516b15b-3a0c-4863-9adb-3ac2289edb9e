<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DealershipResource\Pages;
use App\Filament\Resources\DealershipResource\RelationManagers\InvoicesRelationManager;
use App\Filament\Resources\DealershipResource\RelationManagers\RepairersRelationManager;
use App\Models\Dealership;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class DealershipResource extends Resource
{
    protected static ?string $model = Dealership::class;

    protected static ?string $navigationIcon = 'dealerships';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with('billingRequest');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(100)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Get $get, Set $set, ?string $old, ?string $state) {
                                if ($get('short_name') && $get('short_name') !== Str::shortName($old)) {
                                    return;
                                }

                                $set('short_name', Str::shortName($state));
                            }),
                        Forms\Components\TextInput::make('short_name')
                            ->required()
                            ->maxLength(10),
                        Forms\Components\TextInput::make('contact_first_name')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('contact_last_name')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->maxLength(50),
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(50),
                        Forms\Components\Toggle::make('no_direct_debit')
                            ->inline(false)
                            ->label('No Direct Debit')
                            ->helperText('Check this box if the dealership pays via alternative method.'),
                    ]),
                Forms\Components\Section::make('Address')
                    ->maxWidth('2xl')
                    ->inlineLabel()
                    ->schema([
                        Forms\Components\TextInput::make('address_1')
                            ->maxLength(150),
                        Forms\Components\TextInput::make('address_2')
                            ->maxLength(150),
                        Forms\Components\TextInput::make('city')
                            ->maxLength(50),
                        Forms\Components\TextInput::make('county')
                            ->maxLength(50),
                        Forms\Components\TextInput::make('country')
                            ->maxLength(50),
                        Forms\Components\TextInput::make('postcode')
                            ->maxLength(25),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('account.short_name')
                    ->label('Account')
                    ->toggleable()
                    ->visible(fn () => Auth::user()->isViewingAllRecords())
                    ->limit(40)
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->limit(40)
                    ->searchable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->badge()
                    ->alignCenter()
                    ->getStateUsing(fn (Dealership $record) => match (true) {
                        $record->no_direct_debit => 'Manual payment',
                        $record->requiresPaymentSetup() => 'No payment method',
                        default => 'Direct Debit ('.$record->billingRequest->provider->name().')',
                    })
                    ->color(fn (Dealership $record) => match (true) {
                        $record->no_direct_debit => 'warning',
                        $record->requiresPaymentSetup() => 'danger',
                        default => 'success',
                    })
                    ->icon(fn (Dealership $record) => match (true) {
                        ! $record->requiresPaymentSetup() => 'heroicon-s-check-circle',
                        default => 'heroicon-s-exclamation-triangle',
                    }),
                Tables\Columns\TextColumn::make('contact_last_name')
                    ->label('Contact Name')
                    ->getStateUsing(fn (Dealership $record) => $record->contact_first_name.' '.$record->contact_last_name)
                    ->sortable()
                    ->searchable(['contact_first_name', 'contact_last_name']),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns(3)
                    ->schema([
                        TextEntry::make('name'),
                        TextEntry::make('contact')
                            ->getStateUsing(fn (Dealership $record): string => $record->contact_first_name.' '.$record->contact_last_name),
                        TextEntry::make('email')->label('Email'),
                        TextEntry::make('phone')->label('Phone Number'),
                        TextEntry::make('address')
                            ->getStateUsing(fn (Dealership $record) => explode('|||', $record->fullAddress('|||')))
                            ->listWithLineBreaks(),
                        TextEntry::make('payment_method')
                            ->badge()
                            ->getStateUsing(fn (Dealership $record) => match (true) {
                                $record->no_direct_debit => 'Manual payment',
                                $record->requiresPaymentSetup() => 'No payment method',
                                default => 'Direct Debit ('.$record->billingRequest->provider->name().')',
                            })
                            ->color(fn (Dealership $record) => match (true) {
                                $record->no_direct_debit => 'warning',
                                $record->requiresPaymentSetup() => 'danger',
                                default => 'success',
                            })
                            ->icon(fn (Dealership $record) => match (true) {
                                ! $record->requiresPaymentSetup() => 'heroicon-s-check-circle',
                                default => 'heroicon-s-exclamation-triangle',
                            }),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RepairersRelationManager::class,
            InvoicesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDealerships::route('/'),
            'create' => Pages\CreateDealership::route('/create'),
            'edit' => Pages\EditDealership::route('/{record}/edit'),
            'view' => Pages\ViewDealership::route('/{record}'),
        ];
    }
}
