<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PayLaterAgreementResource\Pages;
use App\Filament\Resources\PayLaterAgreementResource\RelationManagers;
use App\Models\PayLaterAgreement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PayLaterAgreementResource extends Resource
{
    protected static ?string $model = PayLaterAgreement::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('payable_id')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('payable_type')
                    ->required(),
                Forms\Components\Select::make('pay_later_plan_id')
                    ->relationship('payLaterPlan', 'name'),
                Forms\Components\Toggle::make('is_approved')
                    ->required(),
                Forms\Components\TextInput::make('deposit_amount')
                    ->numeric(),
                Forms\Components\TextInput::make('loan_amount')
                    ->numeric(),
                Forms\Components\TextInput::make('commission_rate')
                    ->numeric(),
                Forms\Components\TextInput::make('commission_fixed_fee')
                    ->numeric(),
                Forms\Components\TextInput::make('commission_rate_margin')
                    ->numeric(),
                Forms\Components\TextInput::make('token')
                    ->maxLength(255),
                Forms\Components\TextInput::make('url')
                    ->maxLength(255),
                Forms\Components\TextInput::make('status')
                    ->maxLength(255),
                Forms\Components\TextInput::make('description')
                    ->maxLength(255),
                Forms\Components\TextInput::make('provider_reference')
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('payable.customer.full_name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('payable_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payable_type'),
                Tables\Columns\TextColumn::make('payLaterPlan.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_approved')
                    ->boolean(),
                Tables\Columns\TextColumn::make('amount')
                    ->money()
                    ->sortable(),
//                Tables\Columns\TextColumn::make('deposit_amount')
//                    ->numeric()
//                    ->sortable(),
//                Tables\Columns\TextColumn::make('loan_amount')
//                    ->numeric()
//                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate')
                    ->numeric()
                    ->suffix('%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_fixed_fee')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate_margin')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
            ])
            ->bulkActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayLaterAgreements::route('/'),
            'view' => Pages\ViewPayLaterAgreement::route('/{record}'),
        ];
    }
}
