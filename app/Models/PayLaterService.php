<?php

namespace App\Models;

use App\Enums\PaymentMethod;
use App\Models\Concerns\BillableContract;
use App\Models\Concerns\PayLaterContract;
use App\Models\Traits\HasTenant;
use App\Notifications\CustomerRequiresPaymentMethodNotification;
use App\Services\Payments\DirectDebit\CustomerData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class PayLaterService extends Model implements PayLaterContract, BillableContract
{
    /** @use HasFactory<\Database\Factories\PayLaterServiceFactory> */
    use HasFactory;
    use HasTenant;

    protected $fillable = [
        'account_id',
        'customer_id',
        'vrm',
        'private_plate',
        'vin',
        'vehicle_type',
        'vehicle_make',
        'vehicle_model',
        'vehicle_derivative',
        'engine_capacity',
        'vehicle_colour',
        'fuel_type',
        'transmission_type',
        'registration_date',
        'invoice_amount',
        'deposit_amount',
        'dealer_amount',
    ];

    protected $casts = [
        'registration_date' => 'date',
        'invoice_amount' => 'decimal:2',
        'deposit_amount' => 'decimal:2',
        'dealer_amount' => 'decimal:2',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

//    public function payLaterPlan()
//    {
//        return $this->belongsTo(PayLaterPlan::class);
//    }

    public function payLaterAgreement(): MorphOne
    {
        return $this->morphOne(PayLaterAgreement::class, 'payable');
    }

//    public function sale()
//    {
//        return $this->belongsTo(Sale::class);
//    }

//    public function dealership()
//    {
//        return $this->belongsTo(Dealership::class);
//    }

    public function vrm(): string
    {
        return $this->vrm;
    }

    public function description(): string
    {
        return 'Some description';
    }

    public function toCustomerData(): CustomerData
    {
        return $this->customer->toCustomerData();
    }

    public function sendPaymentSetupNotification(): void
    {
        $this->notify(new CustomerRequiresPaymentMethodNotification($this));
    }

    public function requiresPaymentSetup(): bool
    {
        return $this->payLaterAgreement?->isActive() === false;
    }

    public function getCompletePaymentUrl(): ?string
    {
        return $this->payLaterAgreement->url;
    }

    public function getBillableEntityLabel(): string
    {
        return 'Pay Later Service';
    }

    public function getAccountHolderNameDefault(): string
    {
        return '';
    }

    public function metaDataForPaymentProvider(): array
    {
        return [];
    }

    public function notify($instance): void
    {
        $this->customer->notify($instance);
    }

    public function getPaymentMethod(): ?PaymentMethod
    {
        return PaymentMethod::PAY_LATER;
    }

    public function getPayLaterAgreement(): ?PayLaterAgreement
    {
        return $this->payLaterAgreement;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function getCustomer(): Customer
    {
        return $this->customer;
    }
}
