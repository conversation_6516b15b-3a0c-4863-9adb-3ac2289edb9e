<?php

namespace App\Models;

use App\Models\Traits\HasUuids;
use App\Notifications\SalesOfferNotification;
use Illuminate\Database\Eloquent\Concerns\HasUniqueIds;
use Illuminate\Database\Eloquent\Concerns\HasUniqueStringIds;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesOffer extends Model
{
    use HasUuids;

    /** @use HasFactory<\Database\Factories\SalesOfferFactory> */
    use HasFactory;

    protected $casts = [
        'sent_at' => 'datetime',
    ];

    public function salesLead()
    {
        return $this->belongsTo(SalesLead::class);
    }

    public function warrantyProduct()
    {
        return $this->belongsTo(WarrantyProduct::class);
    }

    public function send()
    {
        $this->salesLead->sale->customer->notify(new SalesOfferNotification($this));
        $this->update(['sent_at' => $this->freshTimestamp()]);
    }
}
