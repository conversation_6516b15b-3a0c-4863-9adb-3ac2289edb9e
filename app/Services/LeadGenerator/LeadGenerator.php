<?php

namespace App\Services\LeadGenerator;

use App\Models\Sale;
use App\Models\SalesLead;

class LeadGenerator
{
    protected $generators = [
        WarrantyRenewalLeadGenerator::class,
        WarrantyUpsellLeadGenerator::class,
    ];

    public function execute()
    {
        foreach($this->generators as $generatorClass) {
            $generator = $this->factory($generatorClass);

            $sales = $generator
                ->query(Sale::query()->whereDoesntHave('salesLead'))
                ->get()
                ->map(fn (Sale $sale) => [
                    'upselling_sale_id' => $sale->id,
                    'created_by_user_id' => auth()->id(),
                    'assigned_to_user_id' => null,
                ]);

            dd($sales->toArray());

            SalesLead::query()->upsert($sales, ['upselling_sale_id']);
        }
    }

    private function factory(mixed $generatorClass):LeadGeneratorContract
    {
        return app($generatorClass);
    }
}
