<?php

namespace App\Services\Payments\PayLater;

use App\Enums\PaymentProvider;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\PayLaterAgreement;
use App\Models\PayLaterPlan;
use App\Models\Sale;

interface PayLaterPaymentProcessor
{
    public function forAccount(Account $account): static;

    /**
     * Get the payment processor identifier
     */
    public function getProcessorIdentifier(): PaymentProvider;

    /**
     * Get Plan Payment Breakdown
     */
    public function getPlanBreakdown(PayLaterPlan $payLaterPlan, mixed $amount): PlanBreakdownData;

    /**
     * Pre-approve a customer for finance
     */
    public function preApprove(Customer $customer): PreApprovalData;

    /**
     * Start a finance application
     */
    public function startApplication(PayLaterAgreement $payLaterAgreement, string $reference): ApplicationRedirectData;

    /**
     * Get the status of an application
     */
    public function getApplicationStatus(string $token): ApplicationStatusData;

    /**
     * Upload an invoice for an approved application
     */
    public function uploadInvoice(string $applicationId, Invoice $invoice): bool;
}
